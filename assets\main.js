function addDragable(el) {
  let startX = 0 // el的scroll横向初始位置
  let gapX = 0 // 鼠标点击时的横向初始位置
  let startY = 0 // el的scroll纵向向初始位置
  let gapY = 0 // 鼠标点击时的纵向初始位置
  el.addEventListener('mousedown', start)
  el.addEventListener('mouseleave', stop) // 移除时解除事件

  function start(event) {
    // 判断是否点击鼠标左键
    if (event.button == 0) {
      gapX = event.clientX
      gapY = event.clientY
      startX = el.scrollLeft
      startY = el.scrollTop
      el.addEventListener('mousemove', move) // document
      el.addEventListener('mouseup', stop)
      el.classList.add('moving')
    }
    // event.preventDefault(); // 阻止默认事件或冒泡 如拖拽时选中文本
    return false
  }

  function move(event) {
    // 移动时的坐标 - 鼠标左键点击时的坐标 = 鼠标移动的相对距离
    var left = event.clientX - gapX
    var top = event.clientY - gapY
    // 滚动条初始坐标 - 移动的相对距离 = 应该滚动后的坐标
    el.scrollTo(startX - left, startY - top) // 横向 纵向
    return false
  }

  function stop() {
    // 鼠标松开，解除绑定
    el.removeEventListener('mousemove', move, false)
    el.removeEventListener('mouseup', stop, false)
    el.classList.remove('moving')
  }
}

setTimeout(() => {
  addDragable(document.querySelector('.v-content-wrapper'))
}, 500)
